# 将用户的问题，拆分为寒喧、情绪、业务问题
import random

from dashscope import Generation

from steps.tools import qwen72B,qwenMax2,qwen14B,qwen3

question = "我的公司需要变更法定代表人需要什么材料？"
messages = [
    {
        "role": "user",
        "content":"""## 角色设定（Role）
你是一名便民服务中心的导服人员，同时具备中文语言专家的能力，擅长从用户对话中提取表单所需字段信息。

## 任务目标（Goal）
从用户与客服的对话上下文 `chatContext` 中，提取出填写表单所需字段的值，字段定义请参考字段集合 `fieldlist`。

## 背景信息（Background）
当前系统日期为：**2025-04-24**

## 字段集合定义（Data）
字段集合定义如下：
```json
{
    "fieldlist": [
        {
            "fieldname": "bllx",
            "min": "",
            "max": "",
            "fieldchinesename": "补领类型",
            "fieldtype": "nvarchar",
            "optionrange": [
                {
                    "itemid": 474,
                    "itemvalue": "离婚",
                    "codeid": 141,
                    "orderno": 0,
                    "itemtext": "离婚"
                },
                {
                    "itemid": 475,
                    "itemvalue": "结婚",
                    "codeid": 141,
                    "orderno": 0,
                    "itemtext": "结婚"
                }
            ],
            "dateformat": ""
        },
        {
            "fieldname": "blyy",
            "min": "",
            "max": "",
            "fieldchinesename": "补领原因",
            "fieldtype": "nvarchar",
            "optionrange": [
                {
                    "itemid": 476,
                    "itemvalue": "遗失",
                    "codeid": 142,
                    "orderno": 0,
                    "itemtext": "遗失"
                },
                {
                    "itemid": 477,
                    "itemvalue": "损毁",
                    "codeid": 142,
                    "orderno": 0,
                    "itemtext": "损毁"
                }
            ],
            "dateformat": ""
        },
        {
            "fieldname": "hydjssjg",
            "min": "",
            "max": "",
            "fieldchinesename": "婚姻登记所属机构",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "djrq",
            "min": "",
            "max": "",
            "fieldchinesename": "登记日期",
            "fieldtype": "DateTime",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "xm",
            "min": "",
            "max": "",
            "fieldchinesename": "姓名",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "xingb",
            "min": "",
            "max": "",
            "fieldchinesename": "性别",
            "fieldtype": "nvarchar",
            "optionrange": [
                {
                    "itemid": 423,
                    "itemvalue": "女",
                    "codeid": 133,
                    "orderno": 0,
                    "itemtext": "女"
                },
                {
                    "itemid": 424,
                    "itemvalue": "男",
                    "codeid": 133,
                    "orderno": 0,
                    "itemtext": "男"
                }
            ],
            "dateformat": ""
        },
        {
            "fieldname": "cym",
            "min": "",
            "max": "",
            "fieldchinesename": "曾用名",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "mz",
            "min": "",
            "max": "",
            "fieldchinesename": "民族",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "sfzhm",
            "min": "",
            "max": "",
            "fieldchinesename": "身份证号码",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "lxdh",
            "min": "",
            "max": "",
            "fieldchinesename": "联系电话",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "gj",
            "min": "",
            "max": "",
            "fieldchinesename": "国籍",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "csrq",
            "min": "",
            "max": "",
            "fieldchinesename": "出生日期",
            "fieldtype": "DateTime",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "zy",
            "min": "",
            "max": "",
            "fieldchinesename": "职业",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "whcd",
            "min": "",
            "max": "",
            "fieldchinesename": "文化程度",
            "fieldtype": "nvarchar",
            "optionrange": [
                {
                    "itemid": 478,
                    "itemvalue": "硕士",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "硕士"
                },
                {
                    "itemid": 479,
                    "itemvalue": "本科",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "本科"
                },
                {
                    "itemid": 480,
                    "itemvalue": "高中",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "高中"
                },
                {
                    "itemid": 481,
                    "itemvalue": "初中",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "初中"
                }
            ],
            "dateformat": ""
        },
        {
            "fieldname": "czhkszd",
            "min": "",
            "max": "",
            "fieldchinesename": "常驻户口所在地",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfxm",
            "min": "",
            "max": "",
            "fieldchinesename": "对方姓名",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfxb",
            "min": "",
            "max": "",
            "fieldchinesename": "对方性别",
            "fieldtype": "nvarchar",
            "optionrange": [
                {
                    "itemid": 423,
                    "itemvalue": "女",
                    "codeid": 133,
                    "orderno": 0,
                    "itemtext": "女"
                },
                {
                    "itemid": 424,
                    "itemvalue": "男",
                    "codeid": 133,
                    "orderno": 0,
                    "itemtext": "男"
                }
            ],
            "dateformat": ""
        },
        {
            "fieldname": "dfcym",
            "min": "",
            "max": "",
            "fieldchinesename": "对方曾用名",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfmz",
            "min": "",
            "max": "",
            "fieldchinesename": "对方民族",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfsfzhm",
            "min": "",
            "max": "",
            "fieldchinesename": "对方身份证号码",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dflxdh",
            "min": "",
            "max": "",
            "fieldchinesename": "对方联系电话",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfgj",
            "min": "",
            "max": "",
            "fieldchinesename": "对方国籍",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfcsrq",
            "min": "",
            "max": "",
            "fieldchinesename": "对方出生日期",
            "fieldtype": "DateTime",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfzy",
            "min": "",
            "max": "",
            "fieldchinesename": "对方职业",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        },
        {
            "fieldname": "dfwhcd",
            "min": "",
            "max": "",
            "fieldchinesename": "对方文化程度",
            "fieldtype": "nvarchar",
            "optionrange": [
                {
                    "itemid": 478,
                    "itemvalue": "硕士",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "硕士"
                },
                {
                    "itemid": 479,
                    "itemvalue": "本科",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "本科"
                },
                {
                    "itemid": 480,
                    "itemvalue": "高中",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "高中"
                },
                {
                    "itemid": 481,
                    "itemvalue": "初中",
                    "codeid": 143,
                    "orderno": 0,
                    "itemtext": "初中"
                }
            ],
            "dateformat": ""
        },
        {
            "fieldname": "dfczhkszd",
            "min": "",
            "max": "",
            "fieldchinesename": "对方常驻户口所在地",
            "fieldtype": "nvarchar",
            "optionrange": "",
            "dateformat": ""
        }
    ]
}
```

## 对话上下文（chatContext）
```
用户：我叫小李，我结婚证丢了，来补办？
客服：好的，请问您配偶的身份证号和姓名。
用户：小美，230105199702010021
客服：请问您和您爱人的学历。
用户：我大学，我老婆初中毕业的
```

## 提取说明（Analysis Instructions）
1. 根据 `fieldlist` 字段定义，从 `chatContext` 中提取字段内容。
2. 提取结果应严格对应字段定义：
  - 如果字段类型为“DateTime”，需符合其 `dateformat` 格式；
  - 如果字段有 `optionrange` 限定，只能填写这些选项内的值。
3. 允许结合上下文进行合理推理，例如“今天”应被解析为背景信息中的系统当前日期。
4. 无法明确识别的字段请**不要返回**。

## 输出格式（ReturnType）
输出为一个 JSON 数组，每项为一个字段和值的键值对，例如：[{"key":"住址","value":"江苏省苏州市工业园区185号"},{"key":"出生日期","value":"2000-6-28"}]。
注意：输出必须为 JSON 格式，不要包含任何多余说明文字。"""
    }
]

# for i in range(10):
#     response = qwen14B(messages)
#     print(response)
#     print(f"=========================第{i}次=============================")

response = qwen3(messages)
print(response)

#总控模块，在拆出业务问题时就拆出多个问题