import asyncio
from dotenv import load_dotenv
load_dotenv()
from browser_use import Agent
from langchain_openai import ChatOpenAI
import os

async def main(): 
    agent = Agent(
        task="""
从https://gjzwfw.www.gov.cn/fwmh/item/v3/item_1110000000001360281000105009000.do中提取相关的事项信息形成事项指南。

1.读取事项名称。
2.读取事项指南的各个要素，例如基本信息，办理流程等，具体项根据网页实际情况获取。
3.最后返回完整的事项指南，以key-value的形式存储至csv中。
""",
        llm=ChatOpenAI(
            model=os.getenv("MODEL_NAME", "Qwen/Qwen3-8B"),
            base_url=os.getenv("OPENAI_API_BASE"),
            api_key=os.getenv("OPENAI_API_KEY"),
            temperature=0.7,
            max_tokens=2000
        ),
        use_vision=False
    )
    await agent.run()

asyncio.run(main()) 